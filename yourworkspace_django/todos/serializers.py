"""
Todo serializers for Django REST Framework
"""

from rest_framework import serializers
from django.utils import timezone
from .models import TodoItem


class TodoItemSerializer(serializers.ModelSerializer):
    """待办事项序列化器"""
    
    class Meta:
        model = TodoItem
        fields = [
            'id', 'title', 'description', 'due_date', 'status', 
            'priority', 'is_current_focus', 'completed_at', 
            'created_at', 'updated_at', 'user'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'completed_at', 'user']
    
    def validate_due_date(self, value):
        """验证截止日期"""
        if value and value < timezone.now().date():
            raise serializers.ValidationError("截止日期不能是过去的时间")
        return value
    
    def validate_status(self, value):
        """验证状态"""
        allowed_statuses = ['pending', 'in_progress', 'completed', 'deferred']
        if value not in allowed_statuses:
            raise serializers.ValidationError(f"状态必须是以下之一: {', '.join(allowed_statuses)}")
        return value
    
    def validate_priority(self, value):
        """验证优先级"""
        allowed_priorities = ['low', 'medium', 'high']
        if value not in allowed_priorities:
            raise serializers.ValidationError(f"优先级必须是以下之一: {', '.join(allowed_priorities)}")
        return value
    
    def create(self, validated_data):
        """创建待办事项"""
        # 从context中获取用户
        user = self.context['request'].user
        validated_data['user'] = user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """更新待办事项"""
        # 如果状态改为completed，设置completed_at
        if validated_data.get('status') == 'completed' and instance.status != 'completed':
            validated_data['completed_at'] = timezone.now()
        elif validated_data.get('status') != 'completed':
            validated_data['completed_at'] = None
        
        return super().update(instance, validated_data)


class TodoItemCreateSerializer(serializers.ModelSerializer):
    """创建待办事项的序列化器"""
    
    class Meta:
        model = TodoItem
        fields = ['title', 'description', 'due_date', 'priority', 'is_current_focus']
    
    def validate_due_date(self, value):
        """验证截止日期"""
        if value and value < timezone.now().date():
            raise serializers.ValidationError("截止日期不能是过去的时间")
        return value
    
    def create(self, validated_data):
        """创建待办事项"""
        user = self.context['request'].user
        validated_data['user'] = user
        validated_data['status'] = 'pending'  # 默认状态
        return TodoItem.objects.create(**validated_data)


class TodoItemUpdateSerializer(serializers.ModelSerializer):
    """更新待办事项的序列化器"""

    class Meta:
        model = TodoItem
        fields = ['title', 'description', 'due_date', 'status', 'priority', 'is_current_focus']
        extra_kwargs = {
            'title': {'required': False},
            'description': {'required': False},
            'due_date': {'required': False},
            'status': {'required': False},
            'priority': {'required': False},
            'is_current_focus': {'required': False},
        }
    
    def validate_due_date(self, value):
        """验证截止日期"""
        if value and value < timezone.now().date():
            raise serializers.ValidationError("截止日期不能是过去的时间")
        return value
    
    def validate_status(self, value):
        """验证状态"""
        allowed_statuses = ['pending', 'in_progress', 'completed', 'deferred']
        if value not in allowed_statuses:
            raise serializers.ValidationError(f"状态必须是以下之一: {', '.join(allowed_statuses)}")
        return value
    
    def validate_priority(self, value):
        """验证优先级"""
        allowed_priorities = ['low', 'medium', 'high']
        if value not in allowed_priorities:
            raise serializers.ValidationError(f"优先级必须是以下之一: {', '.join(allowed_priorities)}")
        return value
    
    def update(self, instance, validated_data):
        """更新待办事项"""
        # 如果状态改为completed，设置completed_at
        if validated_data.get('status') == 'completed' and instance.status != 'completed':
            instance.completed_at = timezone.now()
        elif validated_data.get('status') != 'completed':
            instance.completed_at = None
        
        # 更新其他字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        return instance
