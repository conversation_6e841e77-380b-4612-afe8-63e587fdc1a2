from django.db import models
from django.conf import settings
from django.utils import timezone
from typing import Dict, Any


class TodoItem(models.Model):
    """
    TodoItem model for storing individual to-do tasks.
    Migrated from Flask SQLAlchemy TodoItem model.
    """

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('deferred', 'Deferred'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
    ]

    # Foreign key to User
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='todo_items'
    )

    # Todo fields
    title = models.TextField()
    description = models.TextField(blank=True, null=True)
    due_date = models.DateField(blank=True, null=True)

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    priority = models.Char<PERSON>ield(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='medium'
    )

    # Current focus flag
    is_current_focus = models.BooleanField(default=False)

    # Timestamps
    completed_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'todo_items'
        verbose_name = 'Todo Item'
        verbose_name_plural = 'Todo Items'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['due_date']),
            models.Index(fields=['is_current_focus']),
        ]

    def __str__(self):
        return f'{self.title[:50]}...' if len(self.title) > 50 else self.title

    def save(self, *args, **kwargs):
        """Override save to set completed_at when status changes to completed."""
        if self.status == 'completed' and not self.completed_at:
            self.completed_at = timezone.now()
        elif self.status != 'completed':
            self.completed_at = None
        super().save(*args, **kwargs)

    def to_dict(self) -> Dict[str, Any]:
        """Convert TodoItem instance to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user.id,
            'title': self.title,
            'description': self.description,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'status': self.status,
            'priority': self.priority,
            'is_current_focus': self.is_current_focus,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
        }
