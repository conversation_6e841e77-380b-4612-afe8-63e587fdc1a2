<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { fly } from 'svelte/transition';
  import { todoStore } from '$lib/store/todoStore';
  import type { TodoItem, UpdateTodoPayload, TodoPriority } from '$lib/services/todoService';
  import type { ApiError } from '$lib/services/api';

  // Props
  let {
    todo,
    isOpen = false,
    isLoading: externalIsLoading = false,
    onSaveSuccess = (updatedTodo: TodoItem) => {},
    onCloseRequest = () => {},
  } = $props<{
    todo: TodoItem;
    isOpen?: boolean;
    isLoading?: boolean;
    onSaveSuccess?: (updatedTodo: TodoItem) => void;
    onCloseRequest?: () => void;
  }>();

  // Use the external isLoading value
  let isLoading = $state(externalIsLoading);

  // 表单字段的本地状态
  let title = $state('');
  let description = $state('');
  let dueDate = $state('');
  let priority = $state<TodoPriority>('medium');
  let isCurrentFocus = $state(false);

  // 表单反馈的本地状态
  let errorMessage = $state('');
  let successMessage = $state('');

  // 用于表单的唯一ID
  let formId = $state(`todo-edit-form-${Math.random().toString(36).substring(2)}`);

  // 当todo prop变化时，更新表单字段
  $effect(() => {
    if (todo) {
      title = todo.title || '';
      description = todo.description || '';
      dueDate = todo.due_date || '';
      priority = todo.priority || 'medium';
      isCurrentFocus = todo.is_current_focus || false;
    }
  });

  // 当组件挂载时，添加ESC键监听
  onMount(() => {
    document.addEventListener('keydown', handleKeyDown);
  });

  onDestroy(() => {
    // 移除ESC键监听
    document.removeEventListener('keydown', handleKeyDown);
  });

  // 处理ESC键关闭抽屉
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' && isOpen) {
      handleCancel();
    }
  }

  // 重置表单到原始值
  function resetForm() {
    if (todo) {
      title = todo.title || '';
      description = todo.description || '';
      dueDate = todo.due_date || '';
      priority = todo.priority || 'medium';
      isCurrentFocus = todo.is_current_focus || false;
    }
    errorMessage = '';
    successMessage = '';
  }

  // 处理表单提交
  async function handleSubmit() {
    if (!title.trim()) {
      errorMessage = '标题为必填项。';
      successMessage = '';
      return;
    }

    // 检查是否会超出最大焦点数限制 (仅当设为焦点且之前不是焦点时)
    if (isCurrentFocus && !todo.is_current_focus) {
      const storeState = $todoStore;
      const currentFocusedCount = storeState.todos.filter(
        t => t.is_current_focus && t.status !== 'completed'
      ).length;
      if (currentFocusedCount >= storeState.maxFocusItems) {
        errorMessage = `最多只能将 ${storeState.maxFocusItems} 个项目设为当前焦点。请先取消其他项目的焦点状态。`;
        successMessage = '';
        return;
      }
    }

    isLoading = true;
    errorMessage = '';
    successMessage = '';

    const payload: UpdateTodoPayload = {
      title: title.trim(),
      description: description.trim() || undefined,
      due_date: dueDate || undefined,
      priority,
      is_current_focus: isCurrentFocus,
    };

    try {
      const updatedTodo = await todoStore.editTodo(todo.id, payload);
      if (updatedTodo) {
        successMessage = `待办事项 "${updatedTodo.title}" 更新成功！`;
        onSaveSuccess(updatedTodo);
      } else {
        errorMessage = $todoStore.error || '更新待办事项失败，请重试。';
      }
    } catch (error: any) {
      const apiError = error as ApiError;
      errorMessage = apiError?.message || '更新时发生意外错误。';
      console.error('TodoEditDrawer handleSubmit error:', error);
    } finally {
      isLoading = false;
    }
  }

  // 处理取消
  function handleCancel() {
    resetForm();
    onCloseRequest();
  }

  // 处理点击抽屉外部关闭
  function handleOutsideClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (target.classList.contains('drawer-overlay')) {
      handleCancel();
    }
  }
</script>

<!-- 抽屉覆盖层 -->
{#if isOpen}
  <div
    class="fixed inset-0 z-drawer flex items-center justify-end bg-black bg-opacity-50 drawer-overlay"
    style="z-index: 1040;"
    on:click={handleOutsideClick}
    transition:fly={{ x: 300, duration: 300 }}
  >
    <!-- 抽屉内容 -->
    <div
      class="bg-white dark:bg-gray-800 h-full w-full max-w-md shadow-xl overflow-y-auto"
      style="z-index: 1045;"
      on:click|stopPropagation
      transition:fly={{ x: 300, duration: 300 }}
    >
      <!-- 抽屉头部 -->
      <div
        class="sticky top-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 z-10"
      >
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">编辑待办事项</h2>
          <button
            type="button"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            on:click={handleCancel}
            aria-label="关闭抽屉"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 抽屉主体 -->
      <div class="px-6 py-4">
        <!-- 错误和成功消息 -->
        {#if errorMessage}
          <div class="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
            {errorMessage}
          </div>
        {/if}

        {#if successMessage}
          <div class="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-md">
            {successMessage}
          </div>
        {/if}

        <!-- 表单 -->
        <form id={formId} on:submit|preventDefault={handleSubmit} class="space-y-4">
          <!-- 标题字段 -->
          <div>
            <label
              for="{formId}-title"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              标题 <span class="text-red-500">*</span>
            </label>
            <input
              id="{formId}-title"
              type="text"
              bind:value={title}
              placeholder="输入待办事项标题"
              required
              disabled={isLoading}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>

          <!-- 描述字段 -->
          <div>
            <label
              for="{formId}-description"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              描述
            </label>
            <textarea
              id="{formId}-description"
              bind:value={description}
              placeholder="输入详细描述（可选）"
              rows="3"
              disabled={isLoading}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed resize-vertical"
            ></textarea>
          </div>

          <!-- 截止日期字段 -->
          <div>
            <label
              for="{formId}-due-date"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              截止日期
            </label>
            <input
              id="{formId}-due-date"
              type="date"
              bind:value={dueDate}
              disabled={isLoading}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>

          <!-- 优先级字段 -->
          <div>
            <label
              for="{formId}-priority"
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              优先级
            </label>
            <select
              id="{formId}-priority"
              bind:value={priority}
              disabled={isLoading}
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="low">低</option>
              <option value="medium">中</option>
              <option value="high">高</option>
            </select>
          </div>

          <!-- 当前焦点复选框 -->
          <div class="flex items-center">
            <input
              id="{formId}-current-focus"
              type="checkbox"
              bind:checked={isCurrentFocus}
              disabled={isLoading}
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            />
            <label
              for="{formId}-current-focus"
              class="ml-2 block text-sm text-gray-700 dark:text-gray-300"
            >
              设为当前焦点
            </label>
          </div>
        </form>
      </div>

      <!-- 抽屉底部按钮 -->
      <div
        class="sticky bottom-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-6 py-4"
      >
        <div class="flex space-x-3">
          <button
            type="submit"
            form={formId}
            disabled={isLoading}
            class="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors disabled:cursor-not-allowed flex items-center justify-center"
          >
            {#if isLoading}
              <div
                class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"
              ></div>
              更新中...
            {:else}
              更新
            {/if}
          </button>
          <button
            type="button"
            on:click={handleCancel}
            disabled={isLoading}
            class="flex-1 bg-gray-300 hover:bg-gray-400 disabled:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors disabled:cursor-not-allowed"
          >
            取消
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
