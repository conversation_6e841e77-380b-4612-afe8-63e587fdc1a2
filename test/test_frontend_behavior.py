"""
Frontend behavior simulation tests
Tests that simulate actual frontend interactions to catch integration issues
"""

import pytest
import requests
import json
from typing import Dict, Any, Optional

BASE_URL = "http://127.0.0.1:8000"


def get_test_user_token(username: str = "testuser", password: str = "testpass123") -> Optional[str]:
    """Get authentication token for test user"""
    email = f"{username}@test.com"

    # First try to register the user
    register_data = {
        "username": username,
        "password": password,
        "password_confirm": password,
        "email": email
    }

    try:
        register_response = requests.post(f"{BASE_URL}/api/v1/auth/register/", json=register_data)
        print(f"Register response: {register_response.status_code} - {register_response.text}")
    except Exception as e:
        print(f"Register failed: {e}")

    # Then login using email
    login_data = {
        "email": email,
        "password": password
    }

    try:
        login_response = requests.post(f"{BASE_URL}/api/v1/auth/login/", json=login_data)
        print(f"Login response: {login_response.status_code} - {login_response.text}")

        if login_response.status_code == 200:
            response_data = login_response.json()
            return response_data.get("access_token")
    except Exception as e:
        print(f"Login failed: {e}")

    return None


class FrontendBehaviorSimulator:
    """Simulates frontend behavior by making the same API calls the frontend would make"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "testuser", password: str = "testpass123") -> bool:
        """Simulate frontend login"""
        try:
            self.token = get_test_user_token(username, password)
            if self.token:
                self.session.headers.update({
                    'Authorization': f'Token {self.token}',
                    'Content-Type': 'application/json'
                })
                return True
            return False
        except Exception as e:
            print(f"Login failed: {e}")
            return False
    
    def create_todo(self, title: str, description: str = "", priority: str = "medium") -> Optional[Dict[str, Any]]:
        """Simulate creating a todo from frontend"""
        payload = {
            "title": title,
            "description": description,
            "priority": priority
        }
        
        response = self.session.post(f"{self.base_url}/api/v1/todo/todos/", json=payload)
        if response.status_code == 201:
            return response.json()
        else:
            print(f"Create todo failed: {response.status_code} - {response.text}")
            return None
    
    def toggle_todo_focus(self, todo_id: int, new_focus_state: bool) -> Optional[Dict[str, Any]]:
        """Simulate toggling todo focus from frontend (like clicking star button)"""
        payload = {"is_current_focus": new_focus_state}
        
        print(f"Attempting to toggle focus for todo {todo_id} to {new_focus_state}")
        print(f"Payload: {payload}")
        
        response = self.session.put(f"{self.base_url}/api/v1/todo/todos/{todo_id}/", json=payload)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Toggle focus failed: {response.status_code} - {response.text}")
            return None
    
    def get_todos(self) -> Optional[list]:
        """Get all todos"""
        response = self.session.get(f"{self.base_url}/api/v1/todo/todos/")
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Get todos failed: {response.status_code} - {response.text}")
            return None


@pytest.fixture
def frontend_simulator():
    """Fixture that provides a logged-in frontend simulator"""
    simulator = FrontendBehaviorSimulator()
    assert simulator.login(), "Failed to login for frontend simulation"
    return simulator


def test_todo_focus_toggle_behavior(frontend_simulator):
    """Test the complete flow of toggling todo focus like the frontend does"""
    
    # Step 1: Create a test todo
    todo = frontend_simulator.create_todo(
        title="Test Focus Toggle",
        description="Testing focus toggle behavior",
        priority="high"
    )
    assert todo is not None, "Failed to create test todo"
    assert todo["is_current_focus"] is False, "New todo should not be focused by default"
    
    todo_id = todo["id"]
    
    # Step 2: Set as focus (simulate clicking star button when not focused)
    updated_todo = frontend_simulator.toggle_todo_focus(todo_id, True)
    assert updated_todo is not None, "Failed to set todo as focus"
    assert updated_todo["is_current_focus"] is True, "Todo should be focused after toggle"
    
    # Step 3: Remove from focus (simulate clicking star button when focused)
    updated_todo = frontend_simulator.toggle_todo_focus(todo_id, False)
    assert updated_todo is not None, "Failed to remove todo from focus"
    assert updated_todo["is_current_focus"] is False, "Todo should not be focused after toggle"
    
    # Step 4: Verify the todo still exists and has correct state
    todos = frontend_simulator.get_todos()
    assert todos is not None, "Failed to get todos"
    
    test_todo = next((t for t in todos if t["id"] == todo_id), None)
    assert test_todo is not None, "Test todo should still exist"
    assert test_todo["is_current_focus"] is False, "Final state should be not focused"
